const express = require('express');
const {
  getUserDomainContacts,
  createOrUpdateDomainContact,
  deleteDomain<PERSON>ontact,
  copyDomainContact
} = require('../controllers/userContactController');
const { checkUserOrRefreshToken } = require('../midelwares/authorization');

const userContactRouter = express.Router();

// All routes require authentication
userContactRouter.use(checkUserOrRefreshToken);

// Get user's domain contacts
userContactRouter.get('/domain-contacts', getUserDomainContacts);

// Create or update a domain contact
userContactRouter.post('/domain-contacts', createOrUpdateDomainContact);

// Delete a domain contact
userContactRouter.delete('/domain-contacts/:contactType', deleteDomainContact);

// Copy contact from one type to another
userContactRouter.post('/domain-contacts/copy', copyDomainContact);

module.exports = userContactRouter;
