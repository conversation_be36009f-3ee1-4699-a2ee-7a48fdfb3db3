import apiService from './apiService';

const userContactService = {
  // Get user's domain contacts
  getUserDomainContacts: () =>
    apiService.get('/user-contact/domain-contacts', {
      withCredentials: true,
    }),

  // Create or update a domain contact
  createOrUpdateDomainContact: (data) =>
    apiService.post('/user-contact/domain-contacts', data, {
      withCredentials: true,
    }),

  // Delete a domain contact
  deleteDomainContact: (contactType) =>
    apiService.delete(`/user-contact/domain-contacts/${contactType}`, {
      withCredentials: true,
    }),

  // Copy contact from one type to another
  copyDomainContact: (data) =>
    apiService.post('/user-contact/domain-contacts/copy', data, {
      withCredentials: true,
    }),
};

export default userContactService;
