import React, { useState, useEffect } from "react";
import {
  X,
  User,
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  Copy,
  Check,
} from "lucide-react";
import { toast } from "react-toastify";
import userContactService from "@/app/services/userContactService";
import { useTranslations } from "next-intl";

const DomainContactModal = ({ isOpen, onClose }) => {
  const t = useTranslations("Home");
  const [contacts, setContacts] = useState({
    registrant: null,
    admin: null,
    tech: null,
    billing: null,
  });
  const [activeTab, setActiveTab] = useState("registrant");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    address: "",
    city: "",
    country: "",
    zipcode: "",
    phoneCountryCode: "",
    phone: "",
  });
  const [copiedFrom, setCopiedFrom] = useState(null);

  const contactTypes = [
    {
      key: "registrant",
      label: t ? t("domain.contacts.registrant") : "Registrant",
      icon: User,
    },
    {
      key: "admin",
      label: t ? t("domain.contacts.admin") : "Administrative",
      icon: Building,
    },
    {
      key: "tech",
      label: t ? t("domain.contacts.tech") : "Technical",
      icon: Globe,
    },
    {
      key: "billing",
      label: t ? t("domain.contacts.billing") : "Billing",
      icon: Mail,
    },
  ];

  useEffect(() => {
    if (isOpen) {
      loadContacts();
    }
  }, [isOpen]);

  useEffect(() => {
    // Load form data when active tab changes
    const contact = contacts[activeTab];
    if (contact) {
      setFormData({
        name: contact.name || "",
        email: contact.email || "",
        company: contact.company || "",
        address: contact.address || contact.addressLine1 || "",
        city: contact.city || "",
        country: contact.country || "",
        zipcode: contact.zipcode || "",
        phoneCountryCode: contact.phoneCountryCode || contact.phoneCc || "",
        phone: contact.phone || "",
      });
    } else {
      // Reset form for new contact
      setFormData({
        name: "",
        email: "",
        company: "",
        address: "",
        city: "",
        country: "",
        zipcode: "",
        phoneCountryCode: "",
        phone: "",
      });
    }
  }, [activeTab, contacts]);

  const loadContacts = async () => {
    try {
      setIsLoading(true);
      const response = await userContactService.getUserDomainContacts();
      setContacts(response.data.contacts || {});
    } catch (error) {
      console.error("Error loading contacts:", error);
      toast.error(
        t ? t("domain.contacts.error_loading") : "Error loading contacts"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (!formData.name || !formData.email) {
        toast.error(
          t
            ? t("domain.contacts.name_email_required")
            : "Name and email are required"
        );
        return;
      }

      setIsSaving(true);

      await userContactService.createOrUpdateDomainContact({
        contactType: activeTab,
        contactDetails: formData,
      });

      toast.success(
        t
          ? t("domain.contacts.saved_successfully")
          : "Contact saved successfully"
      );

      // Reload contacts to get updated data
      await loadContacts();
    } catch (error) {
      console.error("Error saving contact:", error);
      toast.error(
        error.response?.data?.error ||
          (t ? t("domain.contacts.error_saving") : "Error saving contact")
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleCopyFrom = async (fromType) => {
    try {
      const sourceContact = contacts[fromType];
      if (!sourceContact) {
        toast.error(
          t ? t("domain.contacts.no_source_contact") : "No source contact found"
        );
        return;
      }

      // Copy the form data
      setFormData({
        name: sourceContact.name || "",
        email: sourceContact.email || "",
        company: sourceContact.company || "",
        address: sourceContact.address || sourceContact.addressLine1 || "",
        city: sourceContact.city || "",
        country: sourceContact.country || "",
        zipcode: sourceContact.zipcode || "",
        phoneCountryCode:
          sourceContact.phoneCountryCode || sourceContact.phoneCc || "",
        phone: sourceContact.phone || "",
      });

      setCopiedFrom(fromType);
      setTimeout(() => setCopiedFrom(null), 2000);

      toast.success(
        t
          ? t("domain.contacts.copied_from").replace("{{type}}", fromType)
          : `Copied from ${fromType} contact`
      );
    } catch (error) {
      console.error("Error copying contact:", error);
      toast.error(
        t ? t("domain.contacts.error_copying") : "Error copying contact"
      );
    }
  };

  const handleDelete = async () => {
    try {
      if (!contacts[activeTab]) {
        toast.error(
          t ? t("domain.contacts.no_contact_to_delete") : "No contact to delete"
        );
        return;
      }

      if (
        !window.confirm(
          t
            ? t("domain.contacts.confirm_delete")
            : "Are you sure you want to delete this contact?"
        )
      ) {
        return;
      }

      setIsSaving(true);

      await userContactService.deleteDomainContact(activeTab);

      toast.success(
        t
          ? t("domain.contacts.deleted_successfully")
          : "Contact deleted successfully"
      );

      // Reload contacts
      await loadContacts();
    } catch (error) {
      console.error("Error deleting contact:", error);
      toast.error(
        error.response?.data?.error ||
          (t ? t("domain.contacts.error_deleting") : "Error deleting contact")
      );
    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {t
              ? t("domain.contacts.manage_contacts")
              : "Manage Domain Contacts"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar - Contact Types */}
          <div className="w-1/3 border-r bg-gray-50 p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-4">
              {t ? t("domain.contacts.contact_types") : "Contact Types"}
            </h3>
            <div className="space-y-2">
              {contactTypes.map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setActiveTab(key)}
                  className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-colors ${
                    activeTab === key
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{label}</span>
                  {contacts[key] && (
                    <div className="ml-auto">
                      <Check className="h-4 w-4 text-green-500" />
                    </div>
                  )}
                </button>
              ))}
            </div>

            {/* Copy From Section */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {t ? t("domain.contacts.copy_from") : "Copy From"}
              </h4>
              <div className="space-y-1">
                {contactTypes
                  .filter(({ key }) => key !== activeTab && contacts[key])
                  .map(({ key, label }) => (
                    <button
                      key={key}
                      onClick={() => handleCopyFrom(key)}
                      className="w-full flex items-center space-x-2 p-2 text-sm text-gray-600 hover:bg-gray-100 rounded"
                    >
                      <Copy className="h-4 w-4" />
                      <span>{label}</span>
                      {copiedFrom === key && (
                        <Check className="h-4 w-4 text-green-500 ml-auto" />
                      )}
                    </button>
                  ))}
              </div>
            </div>
          </div>

          {/* Main Content - Contact Form */}
          <div className="flex-1 p-6 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-gray-500">
                  {t ? t("domain.contacts.loading") : "Loading contacts..."}
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    {contactTypes.find((ct) => ct.key === activeTab)?.label}{" "}
                    {t ? t("domain.contacts.contact") : "Contact"}
                  </h3>
                </div>

                {/* Contact Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.name") : "Name"} *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.email") : "Email"} *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.company") : "Company"}
                    </label>
                    <input
                      type="text"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.address") : "Address"}
                    </label>
                    <input
                      type="text"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.city") : "City"}
                    </label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.country") : "Country"}
                    </label>
                    <input
                      type="text"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.zipcode") : "Zip Code"}
                    </label>
                    <input
                      type="text"
                      name="zipcode"
                      value={formData.zipcode}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t ? t("domain.contacts.phone") : "Phone"}
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        name="phoneCountryCode"
                        value={formData.phoneCountryCode}
                        onChange={handleInputChange}
                        placeholder="+1"
                        className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="text"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between pt-6 border-t">
                  <div>
                    {contacts[activeTab] && (
                      <button
                        onClick={handleDelete}
                        disabled={isSaving}
                        className="px-4 py-2 text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors disabled:opacity-50"
                      >
                        {t ? t("domain.contacts.delete") : "Delete"}
                      </button>
                    )}
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={onClose}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      {t ? t("domain.contacts.cancel") : "Cancel"}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={isSaving || !formData.name || !formData.email}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                    >
                      {isSaving
                        ? t
                          ? t("domain.contacts.saving")
                          : "Saving..."
                        : t
                        ? t("domain.contacts.save")
                        : "Save"}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DomainContactModal;
