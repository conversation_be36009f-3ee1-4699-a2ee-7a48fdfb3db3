const express = require("express");
const passport = require("passport");
const cors = require("cors");
const app = express();
const authRouter = require("./routes/authRouter");
const userRouter = require("./routes/userRouter");
const brandRouter = require("./routes/brandRouter");
const packageRouter = require("./routes/packageRouter");
const specificationRouter = require("./routes/specificationRouter");
const categoryRouter = require("./routes/categoryRouter");
const cartRouter = require("./routes/cartRouter");
const orderRouter = require("./routes/orderRouter");
const payzoneRouter = require("./routes/payzoneRouter");
const paymentRouter = require("./routes/paymentRouter");
const chatbotRouter = require("./routes/chatbotRouter");
const adminRouter = require("./routes/adminRouter");
const ticketsRouter = require("./routes/ticketsRouter");
const sslRouter = require("./routes/sslRouter");
const contactRouter = require("./routes/contactRouter");

const dotenv = require("dotenv");
dotenv.config();
require("./config/db");
require("./services/passport");
const { asyncBackFrontEndLang } = require("./midelwares/sharedMidd");

const i18next = require("./config/i18n");
const i18nextMiddleware = require("i18next-http-middleware");

// Set up i18next middleware for translations
app.use(i18nextMiddleware.handle(i18next)); // Enable i18n middleware

const bodyParser = require("body-parser");
const { isProd } = require("./constants/constant");
app.use(bodyParser.urlencoded({ limit: "10mb", extended: true }));

// Auth error handler
app.use((err, req, res, next) => {
  if (err.name === "AuthenticationError") {
    return res.status(401).json({
      success: false,
      message: err.message || "Authentication failed",
    });
  }
  next(err);
});

// Configure CORS
// const corsOptions = {
//   origin: isProd ? process.env.FRONTEND_URL : process.env.FRONTEND_LOCAL_URL,
//   credentials: true,
// };

// const corsOptions = {
//   origin: function(origin, callback) {
//     const allowedOrigins = [
//       "https://www.ztechengineering.com",
//       isProd ? process.env.FRONTEND_URL : process.env.FRONTEND_LOCAL_URL
//     ];

//     // Allow requests with no origin (like mobile apps, WebViews, or curl requests)
//     if (!origin) return callback(null, true);

//     // Check if origin starts with any of the allowed origins
//     const isAllowed = allowedOrigins.some(allowedOrigin =>
//       origin === allowedOrigin || origin.startsWith(allowedOrigin)
//     );

//     if (isAllowed) {
//       callback(null, true);
//     } else {
//       console.log('Blocked origin:', origin); // For debugging
//       callback(null, false);
//     }
//   },
//   credentials: true,
// };

const allowedOrigins = isProd
  ? [
      "https://ztechengineering.com",
      "https://www.ztechengineering.com",
      "https://ztekengineering.com",
      "https://www.ztekengineering.com",
    ]
  : [process.env.FRONTEND_LOCAL_URL || "http://localhost:3001"];

// Configure CORS
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (e.g., non-browser clients)
    if (!origin) return callback(null, true);

    // Check if origin is in allowed list
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true,
};

app.use(cors(corsOptions));

// JSON & URL encoding middleware
app.use(
  express.json({
    limit: "10mb",
    verify: (req, res, buf) => {
      req.rawBody = buf.toString(); // Stores the raw request body
    },
  })
);
app.use(express.urlencoded({ limit: "10mb", extended: true }));

// Static file serving
app.use(express.static("public"));
app.use("/images", express.static("public/images"));
app.use("/avatars", express.static("public/avatars"));
app.use("/images/uploads", express.static("public/images/uploads"));

const cookieParser = require("cookie-parser");
const domainMngRouter = require("./routes/domainMngRouter");
app.use(cookieParser());
app.use(passport.initialize());

// Apply the asyncBackFrontEndLang middleware globally
app.use(asyncBackFrontEndLang);

// Routes
app.use("/auth", authRouter);
app.use("/user", userRouter);
app.use("/category", categoryRouter);
app.use("/brand", brandRouter);
app.use("/package", packageRouter);
app.use("/specification", specificationRouter);
app.use("/cart", cartRouter);
app.use("/order", orderRouter);
app.use("/payment", payzoneRouter);
app.use("/admin", adminRouter);
app.use("/paymentHistory", paymentRouter);
app.use("/tickets", ticketsRouter);
app.use("/chatbot", chatbotRouter);
app.use("/ssl", sslRouter);
app.use("/domainMng", domainMngRouter);
app.use("/contact", contactRouter);

app.get("/", (req, res) => {
  res.redirect(process.env.FRONTEND_URL);
});

// Import the chatbot cache service
const chatbotCacheService = require("./services/chatbotCacheService");

const PORT = process.env.PORT || 5002;

// Variable to track if this is the first time the server is starting
// This prevents nodemon from triggering the cache update multiple times
let isFirstStart = true;

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);

  // Only initialize the cache on the first start, not on nodemon restarts
  if (isFirstStart) {
    isFirstStart = false;

    // Delay the cache initialization to avoid immediate restart
    setTimeout(() => {
      // Initialize the package cache on server start
      chatbotCacheService
        .updateCache()
        .then(() =>
          console.log("[CHATBOT CACHE] Initial package cache created")
        )
        .catch((err) =>
          console.error(
            "[CHATBOT CACHE] Failed to create initial package cache:",
            err
          )
        );

      // Set up scheduled cache update every 24 hours
      // Using setInterval inside setTimeout to prevent immediate execution
      setInterval(() => {
        chatbotCacheService
          .updateCache()
          .then(() => console.log("[CHATBOT CACHE] Package cache updated"))
          .catch((err) =>
            console.error(
              "[CHATBOT CACHE] Failed to update package cache:",
              err
            )
          );
      }, 24 * 60 * 60 * 1000); // 24 hours
    }, 5000); // 5 second delay
  }
});
