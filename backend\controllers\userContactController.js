const User = require('../models/User');
const contactService = require('../services/contactService');

/**
 * Get user's domain contacts
 */
exports.getUserDomainContacts = async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get contact details for each contact ID if they exist
    const contacts = {};
    
    if (user.domainContacts.regContactId) {
      try {
        contacts.registrant = await contactService.getContactDetails(user.domainContacts.regContactId);
      } catch (error) {
        console.log('Error fetching registrant contact:', error.message);
      }
    }
    
    if (user.domainContacts.adminContactId) {
      try {
        contacts.admin = await contactService.getContactDetails(user.domainContacts.adminContactId);
      } catch (error) {
        console.log('Error fetching admin contact:', error.message);
      }
    }
    
    if (user.domainContacts.techContactId) {
      try {
        contacts.tech = await contactService.getContactDetails(user.domainContacts.techContactId);
      } catch (error) {
        console.log('Error fetching tech contact:', error.message);
      }
    }
    
    if (user.domainContacts.billingContactId) {
      try {
        contacts.billing = await contactService.getContactDetails(user.domainContacts.billingContactId);
      } catch (error) {
        console.log('Error fetching billing contact:', error.message);
      }
    }

    res.json({
      success: true,
      contactIds: user.domainContacts,
      contacts
    });
  } catch (error) {
    console.error('Error getting user domain contacts:', error);
    res.status(500).json({ error: 'Failed to get domain contacts' });
  }
};

/**
 * Create or update user's domain contact
 */
exports.createOrUpdateDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType, contactDetails, customerId } = req.body;
    
    // Validate contact type
    const validTypes = ['registrant', 'admin', 'tech', 'billing'];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: 'Invalid contact type' });
    }

    // Validate required fields
    if (!contactDetails || !contactDetails.name || !contactDetails.email) {
      return res.status(400).json({
        error: 'Contact details with name and email are required'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Use provided customerId or create one if needed
    let customerIdToUse = customerId;
    if (!customerIdToUse) {
      // Create customer if not provided
      const customerDetails = {
        name: contactDetails.name,
        email: contactDetails.email,
        company: contactDetails.company || '',
        address: contactDetails.address || contactDetails.addressLine1 || '',
        city: contactDetails.city || '',
        country: contactDetails.country || '',
        zipcode: contactDetails.zipcode || '',
        phoneCountryCode: contactDetails.phoneCountryCode || contactDetails.phoneCc || '',
        phone: contactDetails.phone || ''
      };
      
      customerIdToUse = await contactService.createCustomer(customerDetails);
    }

    // Map contact type to field name
    const contactFieldMap = {
      registrant: 'regContactId',
      admin: 'adminContactId',
      tech: 'techContactId',
      billing: 'billingContactId'
    };

    const fieldName = contactFieldMap[contactType];
    const existingContactId = user.domainContacts[fieldName];

    let contactId;
    
    if (existingContactId) {
      // Update existing contact
      await contactService.modifyContact(existingContactId, contactDetails);
      contactId = existingContactId;
    } else {
      // Create new contact
      const result = await contactService.addContact(contactDetails, customerIdToUse);
      contactId = result.contactId || result['contact-id'];
    }

    // Update user's contact ID
    user.domainContacts[fieldName] = contactId;
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact ${existingContactId ? 'updated' : 'created'} successfully`,
      contactId,
      customerId: customerIdToUse
    });
  } catch (error) {
    console.error('Error creating/updating domain contact:', error);
    res.status(500).json({ 
      error: 'Failed to create/update domain contact',
      details: error.response?.data || error.message
    });
  }
};

/**
 * Delete user's domain contact
 */
exports.deleteDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { contactType } = req.params;
    
    // Validate contact type
    const validTypes = ['registrant', 'admin', 'tech', 'billing'];
    if (!validTypes.includes(contactType)) {
      return res.status(400).json({ error: 'Invalid contact type' });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Map contact type to field name
    const contactFieldMap = {
      registrant: 'regContactId',
      admin: 'adminContactId',
      tech: 'techContactId',
      billing: 'billingContactId'
    };

    const fieldName = contactFieldMap[contactType];
    const contactId = user.domainContacts[fieldName];

    if (!contactId) {
      return res.status(404).json({ error: 'Contact not found' });
    }

    // Delete contact from external service
    await contactService.deleteContact(contactId);

    // Remove contact ID from user
    user.domainContacts[fieldName] = '';
    await user.save();

    res.json({
      success: true,
      message: `${contactType} contact deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting domain contact:', error);
    res.status(500).json({ 
      error: 'Failed to delete domain contact',
      details: error.response?.data || error.message
    });
  }
};

/**
 * Copy contact details from one type to another
 */
exports.copyDomainContact = async (req, res) => {
  try {
    const userId = req.user._id;
    const { fromType, toType, customerId } = req.body;
    
    // Validate contact types
    const validTypes = ['registrant', 'admin', 'tech', 'billing'];
    if (!validTypes.includes(fromType) || !validTypes.includes(toType)) {
      return res.status(400).json({ error: 'Invalid contact type' });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Map contact type to field name
    const contactFieldMap = {
      registrant: 'regContactId',
      admin: 'adminContactId',
      tech: 'techContactId',
      billing: 'billingContactId'
    };

    const fromFieldName = contactFieldMap[fromType];
    const toFieldName = contactFieldMap[toType];
    const fromContactId = user.domainContacts[fromFieldName];

    if (!fromContactId) {
      return res.status(404).json({ error: `Source ${fromType} contact not found` });
    }

    // Get source contact details
    const sourceContact = await contactService.getContactDetails(fromContactId);
    
    // Create new contact with same details
    const result = await contactService.addContact(sourceContact, customerId);
    const newContactId = result.contactId || result['contact-id'];

    // Update user's contact ID
    user.domainContacts[toFieldName] = newContactId;
    await user.save();

    res.json({
      success: true,
      message: `${fromType} contact copied to ${toType} successfully`,
      contactId: newContactId
    });
  } catch (error) {
    console.error('Error copying domain contact:', error);
    res.status(500).json({ 
      error: 'Failed to copy domain contact',
      details: error.response?.data || error.message
    });
  }
};
