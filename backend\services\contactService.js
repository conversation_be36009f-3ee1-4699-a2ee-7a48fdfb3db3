const axios = require("axios");

// API configuration - using the same configuration as domainMngController
const API_BASE_URL = process.env.API_BASE_URL_TEST;

const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_TEST,
  "api-key": process.env.API_KEY_TEST,
};

/**
 * Add a new contact
 * @param {Object} contactDetails - Contact information
 * @param {string} customerId - Customer ID
 * @returns {Object} API response with contact ID
 */
const addContact = async (contactDetails, customerId) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    name: contactDetails.name,
    company: contactDetails.company || "",
    email: contactDetails.email,
    "address-line-1":
      contactDetails.address || contactDetails.addressLine1 || "",
    city: contactDetails.city || "",
    country: contactDetails.country || "",
    zipcode: contactDetails.zipcode || "",
    "phone-cc": contactDetails.phoneCountryCode || contactDetails.phoneCc || "",
    phone: contactDetails.phone || "",
    "customer-id": customerId,
    type: contactDetails.type || "Contact",
  });

  try {
    const response = await axios.post(
      `${API_BASE_URL}/contacts/add.json?${params.toString()}`,
      null
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error adding contact:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Modify an existing contact
 * @param {string} contactId - Contact ID to modify
 * @param {Object} updatedDetails - Updated contact details
 * @returns {Object} API response
 */
const modifyContact = async (contactId, updatedDetails) => {
  // Clean up the updated details to match API parameter names
  const cleanedDetails = {};

  if (updatedDetails.name) cleanedDetails.name = updatedDetails.name;
  if (updatedDetails.company) cleanedDetails.company = updatedDetails.company;
  if (updatedDetails.email) cleanedDetails.email = updatedDetails.email;
  if (updatedDetails.address || updatedDetails.addressLine1) {
    cleanedDetails["address-line-1"] =
      updatedDetails.address || updatedDetails.addressLine1;
  }
  if (updatedDetails.city) cleanedDetails.city = updatedDetails.city;
  if (updatedDetails.country) cleanedDetails.country = updatedDetails.country;
  if (updatedDetails.zipcode) cleanedDetails.zipcode = updatedDetails.zipcode;
  if (updatedDetails.phoneCountryCode || updatedDetails.phoneCc) {
    cleanedDetails["phone-cc"] =
      updatedDetails.phoneCountryCode || updatedDetails.phoneCc;
  }
  if (updatedDetails.phone) cleanedDetails.phone = updatedDetails.phone;

  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    "contact-id": contactId,
    ...cleanedDetails,
  });

  try {
    const response = await axios.post(
      `${API_BASE_URL}/contacts/modify.json?${params.toString()}`,
      null
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error modifying contact:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Get contact details
 * @param {string} contactId - Contact ID
 * @returns {Object} Contact details
 */
const getContactDetails = async (contactId) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    "contact-id": contactId,
  });

  try {
    const response = await axios.get(
      `${API_BASE_URL}/contacts/get.json?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error getting contact details:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Search contacts
 * @param {Object} searchCriteria - Search parameters
 * @returns {Object} Search results
 */
const searchContacts = async (searchCriteria) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    ...searchCriteria, // e.g., { name: 'John', email: '<EMAIL>' }
  });

  try {
    const response = await axios.get(
      `${API_BASE_URL}/contacts/search.json?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error searching contacts:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Get default contact for a customer
 * @param {string} customerId - Customer ID
 * @returns {Object} Default contact details
 */
const getDefaultContact = async (customerId) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    "customer-id": customerId,
  });

  try {
    const response = await axios.get(
      `${API_BASE_URL}/contacts/default.json?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error getting default contact:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Associate extra details for specific TLDs
 * @param {string} contactId - Contact ID
 * @param {Object} extraDetails - Extra details (e.g., SSN, EIN)
 * @returns {Object} API response
 */
const associateExtraDetails = async (contactId, extraDetails) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    "contact-id": contactId,
    ...extraDetails, // e.g., { ssn: '***********', ein: '12-3456789' }
  });

  try {
    const response = await axios.post(
      `${API_BASE_URL}/contacts/associate-extra.json?${params.toString()}`,
      null
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error associating extra details:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Delete a contact
 * @param {string} contactId - Contact ID to delete
 * @returns {Object} API response
 */
const deleteContact = async (contactId) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    "contact-id": contactId,
  });

  try {
    const response = await axios.post(
      `${API_BASE_URL}/contacts/delete.json?${params.toString()}`,
      null
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error deleting contact:",
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Create a new customer
 * @param {Object} customerDetails - Customer information
 * @returns {string} Customer ID
 */
const createCustomer = async (customerDetails) => {
  const params = new URLSearchParams({
    ...AUTH_PARAMS,
    username: customerDetails.email,
    passwd: customerDetails.password || "defaultPassword123",
    name: customerDetails.name,
    company: customerDetails.company || "",
    "address-line-1":
      customerDetails.address || customerDetails.addressLine1 || "",
    city: customerDetails.city || "",
    country: customerDetails.country || "",
    zipcode: customerDetails.zipcode || "",
    "phone-cc":
      customerDetails.phoneCountryCode || customerDetails.phoneCc || "",
    phone: customerDetails.phone || "",
  });

  try {
    const response = await axios.post(
      `${API_BASE_URL}/customers/add.json?${params.toString()}`,
      null
    );
    return response.data.customerId;
  } catch (error) {
    console.error(
      "Customer creation error:",
      error.response?.data || error.message
    );
    throw error;
  }
};

module.exports = {
  addContact,
  modifyContact,
  getContactDetails,
  searchContacts,
  getDefaultContact,
  associateExtraDetails,
  deleteContact,
  createCustomer,
};
